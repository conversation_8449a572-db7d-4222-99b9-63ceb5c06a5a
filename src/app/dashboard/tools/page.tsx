"use client"

import { useEffect, useState, useMemo } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { PlusIcon, Search, Filter } from "lucide-react"
import { getMarketplaceUrl } from "@/lib/helpers"
import { Ta<PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs"
import { Input } from "@/components/ui/input"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import Link from "next/link"
import Image from "next/image"
import { useMCPServers } from "@/hooks/use-mcp-servers"
import { useDebounce } from "@/hooks/use-debounce"
import { format } from "date-fns"
import { toast } from "sonner"
import { Skeleton } from "@/components/ui/skeleton"
import { AddMCPServerDialog } from "@/components/mcp/add-mcp-server-dialog"



// MCP Server Card Skeleton component
function MCPServerCardSkeleton() {
  return (
    <div className="rounded-lg border bg-card shadow-sm overflow-hidden">
      <div className="p-6">
        <div className="flex items-center justify-between mb-4">
          <Skeleton className="h-6 w-16" />
          <Skeleton className="h-4 w-24" />
        </div>

        <div className="flex items-center gap-2 mb-2">
          <Skeleton className="h-6 w-6 rounded-full" />
          <Skeleton className="h-7 w-3/4" />
        </div>
        <Skeleton className="h-4 w-full mb-6" />

        <div className="space-y-4">
          <div>
            <Skeleton className="h-5 w-20 mb-1" />
            <Skeleton className="h-10 w-full rounded" />
          </div>
          <div>
            <Skeleton className="h-5 w-20 mb-1" />
            <div className="flex items-center gap-2">
              <Skeleton className="h-4 w-4" />
              <Skeleton className="h-5 w-3/4" />
            </div>
          </div>
        </div>
      </div>

      <div className="border-t p-4 flex justify-between items-center bg-muted/30">
        <Skeleton className="h-9 w-24" />
        <Skeleton className="h-5 w-24" />
      </div>
    </div>
  );
}

export default function ToolsPage() {
  // State for dialog
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false);

  // State for filters
  const [searchQuery, setSearchQuery] = useState("");
  const [selectedDepartment, setSelectedDepartment] = useState("");
  const [selectedVisibility, setSelectedVisibility] = useState("");
  const [selectedStatus, setSelectedStatus] = useState("");
  const [selectedUrlType, setSelectedUrlType] = useState("");
  const [selectedDeploymentStatus, setSelectedDeploymentStatus] = useState("");
  const [showFilters, setShowFilters] = useState(false);
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize] = useState(10);

  // Debounce search query with 2 second delay
  const debouncedSearchQuery = useDebounce(searchQuery, 2000);

  // Create filter params for API using useMemo to avoid recreating on every render
  const filterParams = useMemo(() => ({
    page: currentPage,
    page_size: pageSize,
    department: selectedDepartment || undefined,
    visibility: selectedVisibility || undefined,
    status: selectedStatus || undefined,
    url_type: selectedUrlType || undefined,
    deployment_status: selectedDeploymentStatus || undefined,
    search: debouncedSearchQuery || undefined,
  }), [currentPage, pageSize, selectedDepartment, selectedVisibility, selectedStatus, selectedUrlType, selectedDeploymentStatus, debouncedSearchQuery]);

  // Fetch MCP servers data with filters
  const { data: mcpServersData, isLoading, isError, error } = useMCPServers(filterParams);

  // Show error toast when API call fails - only once
  useEffect(() => {
    if (isError && error) {
      const errorMessage = error.response?.data?.message || 'Failed to fetch MCP servers';
      toast.error(errorMessage);
    }
  }, [isError, error]);

  // Handle opening the marketplace
  const handleOpenMarketplace = () => {
    const marketplaceUrl = getMarketplaceUrl() || "https://marketplace.ruh.ai";
    window.open(marketplaceUrl, "_blank");
    toast.info("Opening MCP marketplace in a new tab");
  };

  // Handle opening add MCP server dialog
  const handleAddMCPServer = () => {
    setIsAddDialogOpen(true);
  };

  // Handle search with debounce
  const handleSearch = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setSearchQuery(value);
    setCurrentPage(1);
  };

  // Handle filter changes
  const handleDepartmentChange = (value: string) => {
    setSelectedDepartment(value === "all" ? "" : value);
    setCurrentPage(1);
  };

  const handleVisibilityChange = (value: string) => {
    setSelectedVisibility(value === "all" ? "" : value);
    setCurrentPage(1);
  };

  const handleStatusChange = (value: string) => {
    setSelectedStatus(value === "all" ? "" : value);
    setCurrentPage(1);
  };

  const handleUrlTypeChange = (value: string) => {
    setSelectedUrlType(value === "all" ? "" : value);
    setCurrentPage(1);
  };

  const handleDeploymentStatusChange = (value: string) => {
    setSelectedDeploymentStatus(value === "all" ? "" : value);
    setCurrentPage(1);
  };

  // Handle reset filters
  const handleResetFilters = () => {
    setSearchQuery("");
    setSelectedDepartment("");
    setSelectedVisibility("");
    setSelectedStatus("");
    setSelectedUrlType("");
    setSelectedDeploymentStatus("");
    setCurrentPage(1);
  };

  // Format MCP servers data for display
  const mcpServers = mcpServersData?.data || [];

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold tracking-tight">Platform Integrations</h1>
          <p className="text-muted-foreground">
            Manage your AI tools and MCP servers
          </p>
        </div>
        <div className="flex gap-2">
          <Button variant="outline" onClick={handleOpenMarketplace}>MCP Marketplace</Button>
          <Button className="gap-1" onClick={handleAddMCPServer}>
            <PlusIcon className="h-4 w-4" />
            Add MCP Server
          </Button>
        </div>
      </div>

      <Tabs defaultValue="servers" className="space-y-4">
        <TabsList>
          <TabsTrigger value="servers" className="flex gap-2">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="16"
              height="16"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              strokeWidth="2"
              strokeLinecap="round"
              strokeLinejoin="round"
            >
              <rect width="20" height="8" x="2" y="2" rx="2" ry="2" />
              <rect width="20" height="8" x="2" y="14" rx="2" ry="2" />
              <line x1="6" x2="6.01" y1="6" y2="6" />
              <line x1="6" x2="6.01" y1="18" y2="18" />
            </svg>
            MCP Servers
          </TabsTrigger>
          <TabsTrigger value="tools" className="flex gap-2">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="16"
              height="16"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              strokeWidth="2"
              strokeLinecap="round"
              strokeLinejoin="round"
            >
              <path d="M14.7 6.3a1 1 0 0 0 0 1.4l1.6 1.6a1 1 0 0 0 1.4 0l3.77-3.77a6 6 0 0 1-7.94 7.94l-6.91 6.91a2.12 2.12 0 0 1-3-3l6.91-6.91a6 6 0 0 1 7.94-7.94l-3.76 3.76z" />
            </svg>
            AI Tools
          </TabsTrigger>
        </TabsList>

        <TabsContent value="tools" className="space-y-4">
          {/* No Tools Available State */}
          <div className="flex flex-col items-center justify-center py-16 text-center border rounded-lg bg-card/50">
            <div className="bg-primary/10 p-4 rounded-full mb-4">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="24"
                height="24"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
                className="text-primary"
              >
                <path d="M14.7 6.3a1 1 0 0 0 0 1.4l1.6 1.6a1 1 0 0 0 1.4 0l3.77-3.77a6 6 0 0 1-7.94 7.94l-6.91 6.91a2.12 2.12 0 0 1-3-3l6.91-6.91a6 6 0 0 1 7.94-7.94l-3.76 3.76z" />
              </svg>
            </div>
            <h3 className="text-xl font-medium mb-2">No AI tools available</h3>
            <p className="text-muted-foreground mb-8 max-w-md">
              AI tools are not currently available. Check back later or explore MCP servers for additional capabilities.
            </p>
            {/* <Button variant="outline" size="lg" onClick={handleOpenMarketplace}>
              Browse MCP Marketplace
            </Button> */}
          </div>
        </TabsContent>

        <TabsContent value="servers" className="space-y-4">
          {/* Search and Filters */}
          <div className="flex flex-col gap-4">
            {/* Search Bar */}
            <div className="flex flex-col sm:flex-row gap-4">
              <div className="relative flex-1">
                <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
                <Input
                  type="search"
                  placeholder="Search MCP servers..."
                  className="pl-8 w-full"
                  value={searchQuery}
                  onChange={handleSearch}
                />
              </div>
              <div className="flex gap-2">
                <Button
                  variant="outline"
                  className="sm:w-auto w-full"
                  onClick={() => setShowFilters(!showFilters)}
                >
                  <Filter className="h-4 w-4 mr-2" />
                  Filters
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleResetFilters}
                  className="w-auto"
                >
                  Reset
                </Button>
              </div>
            </div>

            {/* Filters */}
            {showFilters && (
              <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 p-4 bg-card rounded-lg border">
                {/* <div>
                  <label className="text-sm font-medium mb-2 block">Department</label>
                  <Select value={selectedDepartment || "all"} onValueChange={handleDepartmentChange}>
                    <SelectTrigger>
                      <SelectValue placeholder="All Departments" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Departments</SelectItem>
                      <SelectItem value="general">General</SelectItem>
                      <SelectItem value="sales">Sales</SelectItem>
                      <SelectItem value="marketing">Marketing</SelectItem>
                      <SelectItem value="engineering">Engineering</SelectItem>
                      <SelectItem value="finance">Finance</SelectItem>
                      <SelectItem value="hr">HR</SelectItem>
                    </SelectContent>
                  </Select>
                </div> */}

                <div>
                  <label className="text-sm font-medium mb-2 block">Visibility</label>
                  <Select value={selectedVisibility || "all"} onValueChange={handleVisibilityChange}>
                    <SelectTrigger>
                      <SelectValue placeholder="All Visibility" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Visibility</SelectItem>
                      <SelectItem value="private">Private</SelectItem>
                      <SelectItem value="public">Public</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div>
                  <label className="text-sm font-medium mb-2 block">Status</label>
                  <Select value={selectedStatus || "all"} onValueChange={handleStatusChange}>
                    <SelectTrigger>
                      <SelectValue placeholder="All Status" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Status</SelectItem>
                      <SelectItem value="active">Active</SelectItem>
                      <SelectItem value="inactive">Inactive</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                {/* <div>
                  <label className="text-sm font-medium mb-2 block">URL Type</label>
                  <Select value={selectedUrlType || "all"} onValueChange={handleUrlTypeChange}>
                    <SelectTrigger>
                      <SelectValue placeholder="All URL Types" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All URL Types</SelectItem>
                      <SelectItem value="http">HTTP</SelectItem>
                      <SelectItem value="sse">SSE</SelectItem>
                    </SelectContent>
                  </Select>
                </div> */}

                {/* <div>
                  <label className="text-sm font-medium mb-2 block">Deployment Status</label>
                  <Select value={selectedDeploymentStatus || "all"} onValueChange={handleDeploymentStatusChange}>
                    <SelectTrigger>
                      <SelectValue placeholder="All Deployment Status" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Deployment Status</SelectItem>
                      <SelectItem value="deployed">Deployed</SelectItem>
                      <SelectItem value="pending">Pending</SelectItem>
                      <SelectItem value="failed">Failed</SelectItem>
                    </SelectContent>
                  </Select>
                </div> */}
              </div>
            )}
          </div>

          {/* Loading State */}
          {isLoading && (
            <div className="grid gap-6 md:grid-cols-2">
              {Array.from({ length: 2 }).map((_, index) => (
                <MCPServerCardSkeleton key={index} />
              ))}
            </div>
          )}

          {/* Error State */}
          {isError && !isLoading && (
            <div className="flex flex-col items-center justify-center py-16 text-center border rounded-lg bg-card/50">
              <div className="bg-destructive/10 p-4 rounded-full mb-4">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="24"
                  height="24"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  className="text-destructive"
                >
                  <circle cx="12" cy="12" r="10" />
                  <line x1="12" x2="12" y1="8" y2="12" />
                  <line x1="12" x2="12.01" y1="16" y2="16" />
                </svg>
              </div>
              <h3 className="text-xl font-medium mb-2">Failed to load MCP servers</h3>
              <p className="text-muted-foreground mb-8 max-w-md">
                There was an error loading your MCP servers. Please try again later.
              </p>
              <Button
                onClick={() => window.location.reload()}
                variant="outline"
                size="lg"
              >
                Retry
              </Button>
            </div>
          )}

          {/* Empty State */}
          {!isLoading && !isError && mcpServers.length === 0 && (
            <div className="flex flex-col items-center justify-center py-16 text-center border rounded-lg bg-card/50">
              <div className="bg-primary/10 p-4 rounded-full mb-4">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="24"
                  height="24"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  className="text-primary"
                >
                  <rect width="20" height="8" x="2" y="2" rx="2" ry="2" />
                  <rect width="20" height="8" x="2" y="14" rx="2" ry="2" />
                  <line x1="6" x2="6.01" y1="6" y2="6" />
                  <line x1="6" x2="6.01" y1="18" y2="18" />
                </svg>
              </div>
              <h3 className="text-xl font-medium mb-2">No MCP servers found</h3>
              <p className="text-muted-foreground mb-8 max-w-md">
                You don&apos;t have any MCP servers yet. Add your first MCP server to get started.
              </p>
              <Button className="gap-1" size="lg" onClick={handleAddMCPServer}>
                <PlusIcon className="h-5 w-5" />
                Add MCP Server
              </Button>
            </div>
          )}

          {/* MCP Servers List */}
          {!isLoading && !isError && mcpServers.length > 0 && (
            <div className="grid gap-6 md:grid-cols-3">
              {mcpServers.map((server) => (
                <div key={server.id} className="rounded-lg border bg-card shadow-sm overflow-hidden flex flex-col">
                  <div className="p-6 flex-grow">
                    <div className="flex items-center justify-between mb-4">
                      <div className="flex items-center gap-2">
                        <span className={`h-2 w-2 rounded-full ${
                          server.status === 'active' ? 'bg-green-500' : 'bg-gray-400'
                        }`}></span>
                        <span className="text-xs font-medium text-muted-foreground capitalize">
                          {server.status}
                        </span>
                      </div>
                      <span className="text-xs text-muted-foreground">
                        {format(new Date(server.created_at), 'MMM d, yyyy')}
                      </span>
                    </div>

                    <div className="flex items-center gap-3 mb-2">
                      <div className="flex h-10 w-10 items-center justify-center rounded-lg bg-muted flex-shrink-0">
                        <Image
                          src={server.logo && /^https?:\/\//.test(server.logo) ? server.logo : '/assets/mcp.svg'}
                          alt={`${server.name} logo`}
                          width={20}
                          height={20}
                          className="w-5 h-5 object-contain"
                          onError={(e) => {
                            // TypeScript doesn't allow direct assignment to currentTarget.src
                            // Using this workaround
                            const imgElement = e.currentTarget as HTMLImageElement;
                            imgElement.src = '/assets/mcp.svg';
                          }}
                        />
                      </div>
                      <div className="flex-1 min-w-0">
                        <h3 className="text-lg font-semibold truncate">{server.name}</h3>
                        <p className="text-sm text-muted-foreground truncate">
                          {server.category} • {server.config && server.config.length > 0 ? server.config[0].type.toUpperCase() : 'N/A'}
                        </p>
                      </div>
                    </div>
                    <p className="text-sm text-muted-foreground mb-6 line-clamp-2">{server.description || "No description provided"}</p>

                    <div className="space-y-4">
                      <div>
                        {/* <p className="text-sm font-medium text-muted-foreground mb-2">Server URL</p> */}
                        <div className="p-2 rounded flex justify-between items-center">
                          <code className="text-xs font-mono text-muted-foreground truncate flex-1">
                            {server.config && server.config.length > 0 ? server.config[0].url : 'No URL available'}
                          </code>
                          {server.config && server.config.length > 0 && server.config[0].url && (
                            <button
                              className="text-muted-foreground hover:text-foreground ml-2 flex-shrink-0"
                              onClick={() => {
                                navigator.clipboard.writeText(server.config![0].url);
                                toast.success("URL copied to clipboard");
                              }}
                            >
                              <svg
                                xmlns="http://www.w3.org/2000/svg"
                                width="14"
                                height="14"
                                viewBox="0 0 24 24"
                                fill="none"
                                stroke="currentColor"
                                strokeWidth="2"
                                strokeLinecap="round"
                                strokeLinejoin="round"
                              >
                                <rect width="14" height="14" x="8" y="8" rx="2" ry="2" />
                                <path d="M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2" />
                              </svg>
                            </button>
                          )}
                        </div>
                      </div>

                      {server.tags && server.tags.length > 0 && (
                        <div>
                          <p className="text-sm font-medium text-muted-foreground mb-2">Tags</p>
                          <div className="flex flex-wrap gap-1">
                            {server.tags.slice(0, 3).map((tag: string) => (
                              <span key={tag} className="inline-flex items-center px-2 py-1 rounded-md text-xs font-medium bg-muted text-muted-foreground">
                                {tag}
                              </span>
                            ))}
                            {server.tags.length > 3 && (
                              <span className="inline-flex items-center px-2 py-1 rounded-md text-xs font-medium bg-muted text-muted-foreground">
                                +{server.tags.length - 3} more
                              </span>
                            )}
                          </div>
                        </div>
                      )}
                    </div>
                  </div>

                  <div className="border-t p-4 flex justify-end items-center bg-muted/20">
                    {/* <Button variant="outline" size="sm" className="gap-1 text-xs">
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        width="14"
                        height="14"
                        viewBox="0 0 24 24"
                        fill="none"
                        stroke="currentColor"
                        strokeWidth="2"
                        strokeLinecap="round"
                        strokeLinejoin="round"
                      >
                        <path d="M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z" />
                        <circle cx="12" cy="12" r="3" />
                      </svg>
                      Configure
                    </Button> */}
                    <Link
                      href={`/dashboard/tools/servers/${server.id}`}
                      className="flex items-center gap-1 text-sm font-medium text-muted-foreground hover:text-foreground transition-colors"
                    >
                      View Details
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        width="14"
                        height="14"
                        viewBox="0 0 24 24"
                        fill="none"
                        stroke="currentColor"
                        strokeWidth="2"
                        strokeLinecap="round"
                        strokeLinejoin="round"
                      >
                        <path d="M5 12h14" />
                        <path d="m12 5 7 7-7 7" />
                      </svg>
                    </Link>
                  </div>
                </div>
              ))}
            </div>
          )}

          {/* Pagination */}
          {!isLoading && !isError && mcpServers.length > 0 && mcpServersData?.metadata && (
            <div className="flex items-center justify-between mt-6">
              <div className="text-sm text-muted-foreground">
                Showing {mcpServers.length} of {mcpServersData.metadata.total} MCP servers
              </div>
              <div className="flex items-center gap-2">
                <Button
                  variant="outline"
                  size="sm"
                  disabled={currentPage === 1}
                  onClick={() => setCurrentPage(prev => Math.max(1, prev - 1))}
                >
                  Previous
                </Button>
                <div className="text-sm font-medium">
                  Page {currentPage} of {Math.ceil(mcpServersData.metadata.total / pageSize)}
                </div>
                <Button
                  variant="outline"
                  size="sm"
                  disabled={currentPage >= Math.ceil(mcpServersData.metadata.total / pageSize)}
                  onClick={() => setCurrentPage(prev => prev + 1)}
                >
                  Next
                </Button>
              </div>
            </div>
          )}
        </TabsContent>
      </Tabs>

      {/* Add MCP Server Dialog */}
      <AddMCPServerDialog
        open={isAddDialogOpen}
        onOpenChange={setIsAddDialogOpen}
      />
    </div>
  )
}
