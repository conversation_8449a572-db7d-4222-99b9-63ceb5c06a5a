"use client"

import { <PERSON><PERSON> } from "@/components/ui/badge"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Skeleton } from "@/components/ui/skeleton"
import { Switch } from "@/components/ui/switch"
import { Tabs, <PERSON><PERSON><PERSON>ontent, <PERSON>bsList, TabsTrigger } from "@/components/ui/tabs" // Re-added Tabs
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from "@/components/ui/collapsible" // Added Collapsible
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog"
import { AddOutputSchemaDialog } from "@/components/mcp/AddOutputSchemaDialog" // Added import
import { useMCPServer, useDeleteMCPServer, useUpdateMCPServerToolOutputSchema, useToggleMCPServerVisibility } from "@/hooks/use-mcp-servers" // Updated import
import { format } from "date-fns"
import {
  AlertCircle,
  ArrowLeft,
  Copy,
  Edit,
  ExternalLink,
  Eye,
  Github,
  RefreshCw,
  Server,
  Trash2,
  PlusCircle, // Added for the new button
  ChevronDown, // For Collapsible trigger
  ChevronUp // For Collapsible trigger
} from "lucide-react"
import { useParams, useRouter } from "next/navigation"
import { useEffect, useState } from "react"
import { toast } from "sonner"
import { EditMCPServerDialog } from "@/components/mcp/edit-mcp-server-dialog"
import { MCPToolDisplayCard } from "@/components/mcp/MCPToolDisplayCard" // Added import
// Removed axiosClient import as it's no longer directly used here

// Tool Card Skeleton component
function ToolCardSkeleton() {
  return (
    <Card>
      <CardHeader className="pb-2">
        <Skeleton className="h-6 w-3/4 mb-1" />
      </CardHeader>
      <CardContent>
        <Skeleton className="h-4 w-full mb-2" />
        <Skeleton className="h-4 w-5/6" />
      </CardContent>
    </Card>
  );
}



export default function MCPServerDetailsPage() {
  const params = useParams()
  const router = useRouter()
  const id = params.id as string

  // State management
  const [deleteDialog, setDeleteDialog] = useState(false)
  const [visibilityDialog, setVisibilityDialog] = useState(false)
  const [editDialog, setEditDialog] = useState(false)
  const [addSchemaDialog, setAddSchemaDialog] = useState(false) // State for new dialog
  const [selectedToolForSchema, setSelectedToolForSchema] = useState<any | null>(null) // State for selected tool

  // Fetch MCP server data
  const { data, isLoading, isError, error, refetch } = useMCPServer(id)

  // Delete MCP server mutation
  const deleteMCPServer = useDeleteMCPServer()
  const updateToolOutputSchema = useUpdateMCPServerToolOutputSchema() // Added mutation hook
  const toggleMCPServerVisibility = useToggleMCPServerVisibility() // Initialize new hook

  // Show error toast when API call fails - only once
  useEffect(() => {
    if (isError && error) {
      const errorMessage = error.response?.data?.message || 'Failed to fetch MCP server details';
      toast.error(errorMessage);
    }
  }, [isError, error]);

  const handleGoBack = () => {
    router.push('/dashboard/tools');
  };

  const handleRefresh = () => {
    refetch();
    toast.success("Server details refreshed");
  };

  const handleVisibilityToggle = () => {
    setVisibilityDialog(true);
  };

  const handleDeleteServer = () => {
    setDeleteDialog(true);
  };

  const handleEditServer = () => {
    setEditDialog(true);
  };

  const confirmVisibilityChange = async () => {
    if (!id) return;
    try {
      toggleMCPServerVisibility.mutate(id, {
        onSuccess: (response) => {
          setVisibilityDialog(false);
          toast.success(response.message);
        },
        onError: (err: any) => {
          console.error("Error updating visibility:", err);
          const errorMessage = err.response?.data?.message || "Failed to update visibility";
          toast.error(errorMessage);
          setVisibilityDialog(false);
        }
      });
    } catch (error) {
      console.error("Unexpected error while toggling visibility:", error);
      toast.error("An unexpected error occurred.");
      setVisibilityDialog(false);
    }
  };

  const confirmDelete = async () => {
    if (!id) return;

    deleteMCPServer.mutate(id, {
      onSuccess: () => {
        setDeleteDialog(false);
        toast.success("Server deleted successfully");
        router.push('/dashboard/tools');
      },
      onError: (error) => {
        console.error("Error deleting server:", error);
        const errorMessage = error.response?.data?.message || 'Failed to delete server';
        toast.error(errorMessage);
        setDeleteDialog(false);
      }
    });
  };

  const copyToClipboard = (text: string, label: string) => {
    navigator.clipboard.writeText(text);
    toast.success(`${label} copied to clipboard`);
  };

  const handleOpenAddSchemaDialog = (tool: any) => {
    setSelectedToolForSchema(tool);
    setAddSchemaDialog(true);
  };

  const handleAddSchemaSubmit = async (newSchema: object) => {
    if (!data || !selectedToolForSchema || !id) {
      toast.error("Missing data for schema submission.");
      return;
    }

    const payload = {
      mcp_id: id,
      tool_name: selectedToolForSchema.name,
      output_schema_json: newSchema,
    };

    updateToolOutputSchema.mutate(payload, {
      onSuccess: () => {
        toast.success(`Output schema for '${selectedToolForSchema.name}' updated successfully!`);
        // refetch(); // The onSuccess in the hook already invalidates the query
        setAddSchemaDialog(false);
        setSelectedToolForSchema(null);
      },
      onError: (err: any) => {
        console.error("Error updating output schema:", err);
        const errorMessage = err.response?.data?.message || "Failed to update output schema.";
        toast.error(errorMessage);
        // Keep dialog open for user to retry or copy schema if needed
      }
    });
  };


  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <Button variant="ghost" size="sm" onClick={handleGoBack}>
          <ArrowLeft className="h-4 w-4 mr-1" />
          Back to Tools
        </Button>
        <div className="flex items-center gap-2">
          {!isLoading && !isError && data && (
            <>
              <Badge variant="secondary" className="bg-purple-500 text-white">
                {data.mcp.status}
              </Badge>
              <Badge variant="outline" className="bg-gray-100 text-gray-800 dark:bg-gray-900/30 dark:text-gray-400">
                {data.mcp.deployment_status}
              </Badge>
              <Badge variant="outline" className="bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-400">
                <Eye className="h-3 w-3 mr-1" />
                {data.mcp.visibility === "private" ? "Private" : "Public"}
              </Badge>
            </>
          )}
          {!isLoading && !isError && data && data.mcp.is_added===false && (
            <Button variant="outline" size="sm" onClick={handleEditServer}>
              <Edit className="h-4 w-4 mr-1" />
              Edit
            </Button>
          )}
          <Button variant="outline" size="sm" onClick={handleRefresh}>
            <RefreshCw className="h-4 w-4 mr-1" />
            Refresh
          </Button>
        </div>
      </div>

      {/* Title Section */}
      {!isLoading && !isError && data && (
        <div className="flex items-start gap-4">
          {/* Logo */}
          <div className="flex-shrink-0">
            {data.mcp.logo ? (
              <img
                src={data.mcp.logo}
                alt={`${data.mcp.name} logo`}
                className="w-16 h-16 object-contain rounded-lg border bg-white"
              />
            ) : (
              <div className="w-16 h-16 bg-muted rounded-lg flex items-center justify-center border">
                <Github className="h-8 w-8 text-muted-foreground" />
              </div>
            )}
          </div>

          {/* Name and Description */}
          <div className="flex-1 min-w-0">
            <h1 className="text-3xl font-bold tracking-tight">{data.mcp.name}</h1>
            <p className="text-muted-foreground mt-1">
              {data.mcp.description || "Primary MCP server for production environments with advanced AI capabilities and enterprise-grade security features."}
            </p>
          </div>
        </div>
      )}

      {/* Loading State */}
      {isLoading && (
        <div className="space-y-6">
          <div className="flex items-center justify-between">
            <div>
              <Skeleton className="h-8 w-64 mb-2" />
              <Skeleton className="h-4 w-96" />
            </div>
            <Skeleton className="h-10 w-28" />
          </div>

          <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4">
            <Skeleton className="h-24 w-full" />
            <Skeleton className="h-24 w-full" />
            <Skeleton className="h-24 w-full" />
            <Skeleton className="h-24 w-full" />
          </div>

          <div>
            <Skeleton className="h-10 w-40 mb-4" />
            <div className="grid gap-4 grid-cols-1 sm:grid-cols-2 lg:grid-cols-3">
              {Array.from({ length: 6 }).map((_, index) => (
                <ToolCardSkeleton key={index} />
              ))}
            </div>
          </div>
        </div>
      )}

      {/* Error State */}
      {isError && !isLoading && (
        <div className="flex flex-col items-center justify-center py-16 text-center border rounded-lg bg-card/50">
          <div className="bg-destructive/10 p-4 rounded-full mb-4">
            <AlertCircle className="h-12 w-12 text-destructive" />
          </div>
          <h3 className="text-xl font-medium mb-2">Failed to load MCP server details</h3>
          <p className="text-muted-foreground mb-8 max-w-md">
            There was an error loading the MCP server details. Please try again later.
          </p>
          <Button
            onClick={() => window.location.reload()}
            variant="outline"
            size="lg"
          >
            Retry
          </Button>
        </div>
      )}

      {/* Main Content */}
      {!isLoading && !isError && data && (
        <div className="space-y-6">
          <Tabs defaultValue="overview" className="space-y-4">
            <TabsList>
              <TabsTrigger value="overview">Overview</TabsTrigger>
              <TabsTrigger value="tools">Tools</TabsTrigger>
            </TabsList>

            <TabsContent value="overview" className="space-y-6">
              {/* Two Column Layout */}
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                {/* Server Information */}
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <Server className="h-5 w-5" />
                      Server Information
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    {data.mcp.config && data.mcp.config.length > 0 && (
                      <div>
                        {data.mcp.config[0].url && data.mcp.config[0].url !== "string" ? (
                        <div className="flex items-center gap-2 p-2 bg-muted/50 rounded">
                          <code className="text-sm font-mono flex-1">{data.mcp.config[0].url}</code>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => copyToClipboard(data.mcp.config![0].url, "URL")}
                          >
                            <Copy className="h-4 w-4" />
                          </Button>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => window.open(data.mcp.config![0].url, '_blank')}
                          >
                            <ExternalLink className="h-4 w-4" />
                          </Button>
                        </div>
                        ): null}
                      </div>
                    )}

                    {data.mcp.git_url && data.mcp.git_url !== "string" && (
                      <div>
                        <p className="text-sm font-medium text-muted-foreground mb-1">Repository</p>
                        <div className="flex items-center gap-2">
                          <Server className="h-4 w-4 text-muted-foreground" />
                          <span className="text-sm font-mono">{data.mcp.git_url}</span>
                        </div>
                      </div>
                    )}

                    <div>
                      <p className="text-sm font-medium text-muted-foreground mb-1">Category</p>
                      <p className="text-sm capitalize">{data.mcp.category}</p>
                    </div>

                    {data.mcp.git_branch && (
                      <div>
                        <p className="text-sm font-medium text-muted-foreground mb-1">Branch</p>
                        <p className="text-sm">{data.mcp.git_branch}</p>
                      </div>
                    )}

                    {data.mcp.tags && data.mcp.tags.length > 0 && (
                      <div>
                        <p className="text-sm font-medium text-muted-foreground mb-2">Tags</p>
                        <div className="flex flex-wrap gap-1">
                          {data.mcp.tags.map((tag: string) => (
                            <Badge key={tag} variant="secondary" className="text-xs">{tag}</Badge>
                          ))}
                        </div>
                      </div>
                    )}
                  </CardContent>
                </Card>

                {/* Deployment Info */}
                <Card>
                  <CardHeader>
                    <CardTitle>Deployment Info</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="flex justify-between items-center">
                      <span className="text-sm text-muted-foreground">Deployment Status</span>
                      <span className="text-sm font-medium">{data.mcp.deployment_status}</span>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="text-sm text-muted-foreground">Version</span>
                      <span className="text-sm font-medium">v1.2.3</span> {/* Placeholder */}
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="text-sm text-muted-foreground">Deployments</span>
                      <span className="text-sm font-medium">15</span> {/* Placeholder */}
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="text-sm text-muted-foreground">Uptime</span>
                      <span className="text-sm font-medium text-green-600">99.9%</span> {/* Placeholder */}
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="text-sm text-muted-foreground">Last Deployed</span>
                      <span className="text-sm font-medium">
                        {format(new Date(data.mcp.updated_at), 'dd/MM/yyyy')}
                      </span>
                    </div>
                  </CardContent>
                </Card>
              </div>

              {/* Env Keys */}
              <Card>
                <CardHeader>
                  <CardTitle>Env Keys</CardTitle>
                  <p className="text-sm text-muted-foreground">Environment variables used by this MCP server</p>
                </CardHeader>
                <CardContent className="space-y-3">
                  {data.mcp.env_keys && data.mcp.env_keys.length > 0 ? (
                    data.mcp.env_keys.map((envKey: { key: string; description?: string }, index: number) => (
                      <div key={index} className="flex items-center gap-2 p-3 bg-muted/30 rounded">
                        <code className="text-sm font-mono flex-1">{envKey.key}</code>
                        {envKey.description && (
                          <span className="text-sm text-muted-foreground ml-2">{envKey.description}</span>
                        )}
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => copyToClipboard(envKey.key, envKey.key)}
                        >
                          <Copy className="h-4 w-4" />
                        </Button>
                      </div>
                    ))
                  ) : (
                    <div className="text-center p-6 border rounded-lg">
                      <p className="text-muted-foreground">No environment keys available</p>
                    </div>
                  )}
                </CardContent>
              </Card>

              {/* Endpoints */}
              <Card>
                <CardHeader>
                  <CardTitle>Endpoints</CardTitle>
                  <p className="text-sm text-muted-foreground">Available API endpoints for this MCP server</p>
                </CardHeader>
                <CardContent className="space-y-3">
                  {data.mcp.config && data.mcp.config.length > 0 ? (
                    data.mcp.config.map((url, index) => (
                      <div key={index} className="flex items-center gap-2 p-3 bg-muted/30 rounded">
                        <Badge variant="secondary" className="bg-purple-500 text-white text-xs">
                          {url.type.toUpperCase()}
                        </Badge>
                        <code className="text-sm font-mono flex-1">{url.url}</code>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => copyToClipboard(url.url, "Endpoint")}
                        >
                          <Copy className="h-4 w-4" />
                        </Button>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => window.open(url.url, '_blank')}
                        >
                          <ExternalLink className="h-4 w-4" />
                        </Button>
                      </div>
                    ))
                  ) : (
                    <div className="text-center p-6 border rounded-lg">
                      <p className="text-muted-foreground">No endpoints available</p>
                    </div>
                  )}
                </CardContent>
              </Card>

              {/* MCP Server Settings */}
              <Card className="mt-6">
                <CardHeader>
                  <CardTitle>MCP Server Settings</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-6">
                    {data.mcp.is_added===false && (
                    <div className="flex items-center justify-between">
                      <div>
                        <h3 className="font-medium">Public Access</h3>
                        <p className="text-sm text-muted-foreground">Make this server available in the public marketplace</p>
                      </div>
                      <Switch
                        checked={data.mcp.visibility === "public"}
                        onCheckedChange={handleVisibilityToggle}
                        disabled={toggleMCPServerVisibility.isPending}
                      />
                    </div>
                    )}

                    <div className="flex gap-4 pt-6 border-t">
                      <Button
                        variant="outline"
                        className="gap-1 text-destructive hover:text-destructive"
                        onClick={handleDeleteServer}
                        disabled={deleteMCPServer.isPending}
                      >
                        <Trash2 className="h-4 w-4" />
                        Delete Server
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="tools" className="space-y-4">
              {/* MCP Tools Section */}
              {data.mcp.mcp_tools_config && data.mcp.mcp_tools_config.tools && data.mcp.mcp_tools_config.tools.length > 0 ? (
                <Card>
                  <CardHeader>
                    <CardTitle>MCP Tools</CardTitle>
                    <p className="text-sm text-muted-foreground">
                      Available tools provided by this MCP server
                    </p>
                  </CardHeader>
                  <CardContent className="p-4">
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-2 gap-3">
                      {data.mcp.mcp_tools_config.tools.map((tool: any, index: number) => (
                        <MCPToolDisplayCard
                          key={index}
                          tool={tool}
                          onOpenAddSchemaDialog={handleOpenAddSchemaDialog}
                        />
                      ))}
                    </div>
                  </CardContent>
                </Card>
              ) : (
                 <div className="text-center p-10 border rounded-lg">
                    <p className="text-muted-foreground">No tools available for this MCP server.</p>
                  </div>
              )}
            </TabsContent>
          </Tabs>
        </div>
      )}

      {/* Confirmation Dialogs */}
      <AlertDialog open={deleteDialog} onOpenChange={setDeleteDialog}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Delete Server</AlertDialogTitle>
            <AlertDialogDescription>
              Are you sure you want to delete this MCP server? This action cannot be undone.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel disabled={deleteMCPServer.isPending}>Cancel</AlertDialogCancel>
            <AlertDialogAction
              onClick={confirmDelete}
              disabled={deleteMCPServer.isPending}
              className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
            >
              {deleteMCPServer.isPending ? "Deleting..." : "Delete"}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>

      <AlertDialog open={visibilityDialog} onOpenChange={setVisibilityDialog}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>
              {data?.mcp.visibility === "public" ? "Make Server Private" : "Make Server Public"}
            </AlertDialogTitle>
            <AlertDialogDescription>
              {data?.mcp.visibility === "public"
                ? "Are you sure you want to make this server private? It will no longer be visible in the public marketplace."
                : "Are you sure you want to make this server public? It will be visible in the public marketplace."
              }
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction onClick={confirmVisibilityChange}
              disabled={toggleMCPServerVisibility.isPending}
            >
              {toggleMCPServerVisibility.isPending ? "Updating..." : data?.mcp.visibility === "public" ? "Make Private" : "Make Public"}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>

      {/* Edit MCP Server Dialog */}
      {data && (
        <EditMCPServerDialog
          open={editDialog}
          onOpenChange={setEditDialog}
          mcpServer={data.mcp}
          onSuccess={() => {
            refetch()
            toast.success("MCP server updated successfully")
          }}
        />
      )}

      {/* Add Output Schema Dialog */}
      {selectedToolForSchema && (
        <AddOutputSchemaDialog
          open={addSchemaDialog}
          onOpenChange={setAddSchemaDialog}
          toolName={selectedToolForSchema.name}
          onSubmit={handleAddSchemaSubmit}
        />
      )}
    </div>
  )
}
