"use client"

import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from "@/components/ui/collapsible";
import { ChevronDown, ChevronUp, PlusCircle, Eye } from "lucide-react";
import { MCPServer } from '@/services/mcp-service'; // Assuming MCPTool type is part of MCPServer or a related import
import { ViewSchemaDialog } from './ViewSchemaDialog'; // Import the new dialog component

// Define a more specific type for the tool prop if available, e.g., from mcp-service.ts
// For now, using 'any' as a placeholder based on the original page.tsx
interface MCPToolDisplayCardProps {
  tool: any; // Replace 'any' with a more specific tool type if defined
  onOpenAddSchemaDialog: (tool: any) => void;
}

type OpenSection = "input" | "output" | null;

export const MCPToolDisplayCard: React.FC<MCPToolDisplayCardProps> = ({ tool, onOpenAddSchemaDialog }) => {
  const [openSection, setOpenSection] = useState<OpenSection>(null);
  const [viewSchemaDialogOpen, setViewSchemaDialogOpen] = useState(false);
  const [schemaToView, setSchemaToView] = useState<object | null>(null);
  const [schemaNameForDialog, setSchemaNameForDialog] = useState('');

  const handleToggleSection = (section: OpenSection) => {
    setOpenSection(prevOpenSection => (prevOpenSection === section ? null : section));
  };

  const handleViewSchema = (schema: object, name: string) => {
    setSchemaToView(schema);
    setSchemaNameForDialog(name);
    setViewSchemaDialogOpen(true);
  };

  const schemaDisplayMaxHeight = "max-h-[120px]"; 
  const cardMinHeight = "min-h-[150px]";

  return (
    // Removed max-h from Card to allow expansion. Added min-h for base size.
    <Card className={`overflow-hidden flex flex-col ${cardMinHeight}`}>
      <CardHeader className="bg-muted/20 p-2 flex-shrink-0">
        <CardTitle className="text-sm font-medium">{tool.name}</CardTitle>
        <p className="text-xs text-muted-foreground h-10 overflow-y-auto flex-shrink-0"> {/* Removed mb-1 */}
          {tool.description}
        </p>
      </CardHeader>
      <CardContent className="p-2 space-y-1 flex-grow flex flex-col"> {/* Removed overflow-hidden here to allow card to grow */}

        
        <div className="space-y-0.5 flex flex-col mt-auto"> {/* Removed flex-grow from here */}
          {/* Input Schema */}
          <div className="flex items-center justify-between text-xs px-1.5 h-6 w-full">
            <span className="font-medium">Input Schema</span>
            {tool.input_schema ? (
              <Button
                variant="link"
                size="sm"
                onClick={() => handleViewSchema(tool.input_schema, 'Input Schema')}
                className="text-xs h-5 px-0 py-0.5"
              >
                View Input Schema
              </Button>
            ) : (
              <span className="text-muted-foreground">No input schema</span>
            )}
          </div>

          {/* Output Schema */}
          <div className="flex items-center justify-between text-xs px-1.5 h-6 w-full">
            <span className="font-medium">Output Schema</span>
            {tool.output_schema ? (
              <Button
                variant="link"
                size="sm"
                onClick={() => handleViewSchema(tool.output_schema, 'Output Schema')}
                className="text-xs h-5 px-0 py-0.5"
              >
                View Output Schema
              </Button>
            ) : (
              <Button
                variant="link"
                size="sm"
                onClick={() => onOpenAddSchemaDialog(tool)}
                className="text-xs h-5 px-0 py-0.5 text-orange-500"
              >
                No output schema available
              </Button>
            )}
          </div>
        </div>
      </CardContent>
      <ViewSchemaDialog
        open={viewSchemaDialogOpen}
        onOpenChange={setViewSchemaDialogOpen}
        schemaName={schemaNameForDialog}
        schema={schemaToView}
      />
    </Card>
  );
};