"use client"

import { <PERSON>, <PERSON><PERSON>ontent, CardDescription, Card<PERSON><PERSON>er, CardTitle } from "@/components/ui/card"
import { AlertTriangle, CheckCircle, Info, XCircle, ArrowRight } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import Link from "next/link"
import { Activity as ApiActivity } from "@/shared/interfaces";
import { Skeleton } from "@/components/ui/skeleton";

type ActivityType = 'error' | 'success' | 'warning' | 'info'

interface ActivityItem {
  id: string
  title: string
  description: string
  timestamp: string
  type: ActivityType
  link?: string
}

interface ActivityFeedProps {
  activities: ApiActivity[];
  isLoading: boolean;
}

const getActivityIcon = (type: ActivityType) => {
  switch (type) {
    case 'error':
      return <XCircle className="h-5 w-5 text-red-500" />
    case 'warning':
      return <AlertTriangle className="h-5 w-5 text-amber-500" />
    case 'success':
      return <CheckCircle className="h-5 w-5 text-green-500" />
    case 'info':
      return <Info className="h-5 w-5 text-blue-500" />
    default:
      return <Info className="h-5 w-5 text-blue-500" />
  }
}

export function ActivityFeed({ activities, isLoading }: ActivityFeedProps) {
  return (
    <Card className="h-full">
      <CardHeader className="p-4 md:p-6">
        <CardTitle className="text-base">Recent Activity & Issues</CardTitle>
        <CardDescription>
          Latest customer inquiries and system activity
        </CardDescription>
      </CardHeader>
      <CardContent className="p-0">
        <div className="space-y-0">
          {isLoading ? (
            Array.from({ length: 5 }).map((_, index) => (
              <div key={index} className="flex items-start gap-3 p-4 border-b border-border last:border-0 animate-pulse">
                <Skeleton className="h-5 w-5 rounded-full" />
                <div className="flex-1 space-y-2">
                  <Skeleton className="h-4 w-3/4" />
                  <Skeleton className="h-3 w-1/2" />
                  <Skeleton className="h-3 w-1/4" />
                </div>
              </div>
            ))
          ) : activities.length === 0 ? (
            <div className="p-4 text-center text-muted-foreground h-32">
              No recent activity.
            </div>
          ) : (
            activities.map((item) => (
              <Link 
                href={`/dashboard/activity/${item.id}`}
                key={item.id}
                className="block border-b border-border last:border-0 hover:bg-accent/5"
              >
                <div className="flex items-start gap-3 p-4">
                  <div className="pt-0.5">
                    {getActivityIcon(item.status as ActivityType)}
                  </div>
                  <div className="flex-1 min-w-0">
                    <p className="font-medium">{item.type}</p>
                    <p className="text-sm text-muted-foreground line-clamp-1">{item.event_source_type} - {item.resource_id}</p>
                    <p className="text-xs text-muted-foreground mt-1">{new Date(item.created_at).toLocaleString()}</p>
                  </div>
                </div>
              </Link>
            ))
          )}
        </div>
        <div className="p-4 flex justify-center">
          <Button asChild variant="ghost" size="sm" className="w-full">
            <Link href="/dashboard/activity" className="flex items-center justify-center gap-1">
              View Full Activity Log 
              <ArrowRight className="h-3.5 w-3.5 ml-1" />
            </Link>
          </Button>
        </div>
      </CardContent>
    </Card>
  )
}
